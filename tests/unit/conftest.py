import json
import os
import sys
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest
from aws_lambda_powertools.utilities.typing import LambdaContext

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))


@pytest.fixture
def lambda_context():
    """Create a mock Lambda context object"""
    context = MagicMock(spec=LambdaContext)
    context.function_name = "test-function"
    context.aws_request_id = "test-request-id"
    context.invoked_function_arn = (
        "arn:aws:lambda:us-east-1:************:function:test-function"
    )
    return context


@pytest.fixture
def mock_settings():
    """Mock settings for testing"""
    with patch("email_notifications.app.settings") as mock_settings:
        mock_settings.ENV = "TEST"
        mock_settings.DEBUG = True
        mock_settings.AWS_REGION = "us-east-1"
        mock_settings.SLACK_SNS_TOPIC_ARN = (
            "arn:aws:sns:us-east-1:************:test-topic"
        )
        mock_settings.AWS_BUCKET_NAME = "test-bucket"
        mock_settings.POSTGRES_HOST = "test-host"
        mock_settings.POSTGRES_DB = "test-db"
        yield mock_settings


@pytest.fixture
def mock_db_functions():
    """Mock database initialization and closing functions"""
    with (
        patch(
            "email_notifications.app.init_db", new_callable=AsyncMock
        ) as mock_init_db,
        patch(
            "email_notifications.app.close_db", new_callable=AsyncMock
        ) as mock_close_db,
    ):
        yield mock_init_db, mock_close_db


@pytest.fixture
def mock_email_handler():
    """Mock EmailHandler class and its methods"""
    with patch("email_notifications.app.EmailHandler") as mock_handler_class:
        mock_handler = MagicMock()
        mock_handler.send_new_participant_email = AsyncMock(
            return_value="test/path/to/file.csv"
        )
        mock_handler.send_new_module_starting_email = AsyncMock()
        mock_handler.new_participant_joined_email = AsyncMock()
        mock_handler.send_reset_password_email = AsyncMock()
        mock_handler.send_welcome_email = AsyncMock()
        mock_handler.send_disenrolled_email = AsyncMock()
        mock_handler.send_cancelled_session_email = AsyncMock()
        mock_handler.send_cohort_ended_email = AsyncMock()
        mock_handler.send_cohort_ending_in_28_days_email = AsyncMock()

        mock_handler_class.return_value = mock_handler
        yield mock_handler


@pytest.fixture
def mock_sns_client():
    """Mock SNS client"""
    with patch("email_notifications.app.sns_client") as mock_sns:
        yield mock_sns


@pytest.fixture
def mock_presigned_url():
    """Mock create_presigned_url_view function"""
    with patch("email_notifications.app.create_presigned_url_view") as mock_url:
        mock_url.return_value = (
            "https://test-bucket.s3.amazonaws.com/test/path/to/file.csv"
        )
        yield mock_url


@pytest.fixture
def mock_publish_to_sns():
    """Mock publish_to_sns function"""
    with patch("email_notifications.app.publish_to_sns") as mock_publish:
        yield mock_publish


def create_cloudwatch_event(rule_name):
    """Helper function to create CloudWatch event with specified rule name"""
    return {
        "version": "0",
        "id": "abcd1234-5678-90ab-cdef-EXAMPLE",
        "detail-type": "Scheduled Event",
        "source": "aws.events",
        "account": "************",
        "time": "2024-09-16T12:00:00Z",
        "region": "us-east-1",
        "resources": [f"arn:aws:events:us-east-1:************:rule/{rule_name}"],
        "detail": {},
    }


def create_sqs_event(email_event, data, event_type="sqs"):
    """Helper function to create SQS event with specified email_event and data"""
    return {
        "Records": [
            {
                "body": json.dumps(
                    {
                        "type": event_type,
                        "email_event": email_event,
                        "data": data,
                        "correlation_id": "test-correlation-id",
                    }
                )
            }
        ]
    }
