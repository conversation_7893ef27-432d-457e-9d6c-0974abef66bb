import json
import pytest
from unittest.mock import patch, MagicMock

from email_notifications.app import handle_sqs_event
from ciba_participant.common.aws_handler import NotificationType, EmailNotificationEvent


@pytest.mark.asyncio
async def test_handle_sqs_event_unsupported_email_event(mock_email_handler):
    """Test handle_sqs_event function with unsupported email_event"""
    # Arrange
    # Create an event with a valid email_event
    event = {
        "Records": [
            {
                "body": json.dumps(
                    {
                        "type": NotificationType.SQS.value,
                        "email_event": EmailNotificationEvent.NEW_PARTICIPANT.value,
                        "data": {"participant_id": "test-participant-id"},
                        "correlation_id": "test-correlation-id",
                    }
                )
            }
        ]
    }

    # Mock the SQSNotification class to simulate an unsupported event
    with patch("email_notifications.app.SQSNotification") as mock_sqs_notification:
        # Configure the mock to return an object with an email_event that doesn't match any condition
        mock_instance = MagicMock()
        mock_instance.email_event = MagicMock()
        # Make sure it doesn't equal any of the known event types
        mock_instance.email_event.__eq__.side_effect = lambda x: False
        mock_sqs_notification.return_value = mock_instance

        # Act
        with patch("email_notifications.app.logger") as mock_logger:
            result = await handle_sqs_event(event, mock_email_handler)

        # Assert
        mock_logger.warning.assert_called_once_with("Unsupported event type")
        assert result == {
            "statusCode": 400,
            "body": json.dumps("Unsupported event source"),
        }
