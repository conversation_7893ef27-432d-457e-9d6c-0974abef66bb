AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
  python3.12

  Sample SAM Template for scheduled_email_notifications

Parameters:
  LambdaImageUri:
    Type: String
    Description: ECR Image URI for the ParticipantScheduledEmails function
    Default: "572827854243.dkr.ecr.us-east-2.amazonaws.com/scheduled_email_notifications"
  Environment:
    Type: String
    Description: Environment
    Default: "dev"
  DatadogApiKey:
    Type: String
    Description: Datadog API Key
    Default: ""
  Version:
    Type: String
    Description: Version
    Default: "0.0.0"
  Debug:
    Type: Number
    Description: Debug
    Default: "0"
  SendgridApiKey:
    Type: String
    Description: Sendgrid API Key
    Default: ""
  SecurityGroupIds:
    Type: List<String>
    Description: Security Group IDs
    Default: "sg-0c6d9bdb426027ffa"
  SubnetIds:
    Type: List<String>
    Description: Subnet IDs
  ExistingBucketName:
    Type: String
    Description: Existing S3 bucket name
    Default: "participant"
  DataFolderName:
    Type: String
    Description: Data folder name
    Default: "enrolled"
  IsNewEnv:
    Type: Number
    Default: 0
  IsLambda:
    Type: Number
    Default: 1
  KmsKeyId:
    Type: String
    Description: "Participant KMS key ID"

  QueueName:
    Type: String
    Description: Queue Name
    Default: "participant-email-notifications"
  DeadLetterQueueName:
    Type: String
    Description: Dead Letter Queue Name
    Default: "participant-email-notifications-dlq"

Globals:
  Function:
    Timeout: 300
    LoggingConfig:
      LogFormat: JSON

Resources:
  # Dead Letter Queue
  DeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "${Environment}-${DeadLetterQueueName}"

  MainQueue:
    Type: AWS::SQS::Queue
    Properties:
      DelaySeconds: 0
      VisibilityTimeout: 300
      QueueName: !Sub "${Environment}-${QueueName}"

      RedrivePolicy:
        deadLetterTargetArn: !GetAtt DeadLetterQueue.Arn
        maxReceiveCount: 500

  ParticipantScheduledEmails:
    Type: AWS::Serverless::Function # More info about Function Resource: https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md#awsserverlessfunction
    Properties:
      PackageType: Image
      ImageUri: !Ref LambdaImageUri
      Role: !GetAtt ParticipantScheduledEmailFunctionRole.Arn
      Environment:
        Variables:
          ENV: !Ref Environment
          VERSION: !Ref Version
          DEBUG: !Ref Debug
          SENDGRID_API_KEY: !Ref SendgridApiKey
          IS_NEW_ENV: !Ref IsNewEnv
          IS_LAMBDA: !Ref IsLambda
      Events:
        Scales:
          Type: Schedule
          Properties:
            Name: !Sub "Scales-${Environment}"
            Schedule: cron(0 16 * * ? *)
        NewModule:
          Type: Schedule
          Properties:
            Name: !Sub "NewModule-${Environment}"
            Schedule: cron(5 17 * * ? *)
        CohortEndIn28Days:
          Type: Schedule
          Properties:
            Name: !Sub "CohortEndIn28Days-${Environment}"
            Schedule: cron(30 18 * * ? *)
        CohortEndTomorrowAdminReminder:
          Type: Schedule
          Properties:
            Name: !Sub "CohortEndTomorrowAdminReminder-${Environment}"
            Schedule: cron(10 17 * * ? *)
        ClassIn24Hours:
          Type: Schedule
          Properties:
            Name: !Sub "ClassIn24Hours-${Environment}"
            Schedule: rate(30 minutes)


        ParticipantEmailNotificationsSQSEvent:
          Type: SQS
          Properties:
            Queue: !GetAtt MainQueue.Arn
            BatchSize: 10
            Enabled: true
      VpcConfig:
        SecurityGroupIds: !Ref SecurityGroupIds
        SubnetIds: !Ref SubnetIds
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: ./scheduled_email_notifications
  ParticipantScheduledEmailFunctionRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: "Allow"
            Principal:
              Service: "lambda.amazonaws.com"
            Action: "sts:AssumeRole"
      Policies:
        - PolicyName: "ParticipantKMSDecrypt"
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: "Allow"
                Action:
                  - "kms:Decrypt"
                  - "kms:GenerateDataKey"
                Resource: !Sub arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/${KmsKeyId}


        - PolicyName: "ParticipantScheduledEmailPolicy"
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: "Allow"
                Action:
                  - "ssm:GetParameter"
                  - "ssm:GetParameters"
                Resource: "*"
              - Effect: 'Allow'
                Action:
                  - 's3:PutObject'
                  - 's3:GetObject'
                Resource: !Sub 'arn:aws:s3:::${ExistingBucketName}-${Environment}/${DataFolderName}/*'
              - Effect: "Allow"
                Action:
                  - "sqs:ReceiveMessage"
                  - "sqs:DeleteMessage"
                  - "sqs:GetQueueAttributes"
                  - "sqs:SendMessage"
                Resource: "*"
              - Effect: "Allow"
                Action:
                  - "ec2:CreateNetworkInterface"
                  - "ec2:DescribeNetworkInterfaces"
                  - "ec2:DeleteNetworkInterface"
                Resource: "*"
              - Effect: "Allow"
                Action:
                  - "sns:Publish"
                Resource:
                  - "*"
        - PolicyName: "LambdaCloudWatchLogsPolicy"
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: "Allow"
                Action:
                  - "logs:CreateLogGroup"
                  - "logs:CreateLogStream"
                  - "logs:PutLogEvents"
                Resource: "arn:aws:logs:*:*:*"
Outputs:
  LambdaFunctionName:
    Description: "ParticipantScheduledEmails Lambda Function ARN"
    Value: !GetAtt ParticipantScheduledEmails.Arn
  ParticipantScheduledEmailFunctionRole:
    Description: "Implicit IAM Role created for ParticipantScheduledEmailFunctionRole function"
    Value: !GetAtt ParticipantScheduledEmailFunctionRole.Arn
  MainQueue:
    Description: "Main Queue ARN"
    Value: !GetAtt MainQueue.Arn
  DeadLetterQueue:
    Description: "Dead Letter Queue ARN"
    Value: !GetAtt DeadLetterQueue.Arn
