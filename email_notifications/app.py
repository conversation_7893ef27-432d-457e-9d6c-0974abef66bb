import asyncio
import j<PERSON>
from typing import Optional

import boto3
import pendulum
from aws_lambda_powertools import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext
from ciba_participant.common.aws_handler import (
    EmailNotificationEvent,
    SQSNotification,
    create_presigned_url_view,
    publish_to_sns,
)
from ciba_participant.common.db import close_db, init_db
from ciba_participant.notifications.email.send_grid_email import (
    EmailHandler,
    get_participants_with_class_in_24_hours,
    get_participants_with_cohort_starting_tomorrow,
)
from ciba_participant.settings import get_settings
from pydantic import BaseModel

logger = Logger()
settings = get_settings()
sns_client = boto3.client("sns", region_name=settings.AWS_REGION)
DB_INITIALIZED = False


class SlackNotification(BaseModel):
    environment: str
    is_test: bool
    source: str
    title: Optional[str] = ""
    url: Optional[str] = ""
    type: Optional[str] = ""
    details: Optional[str] = ""
    additional_info: Optional[str] = ""


async def ensure_db_initialized():
    """
    Ensure the database is initialized
    to correctly handle aws db connection
    :return:
    """
    global DB_INITIALIZED  # pylint: disable=global-statement
    if not DB_INITIALIZED:
        await init_db()
        DB_INITIALIZED = True
        logger.info("DB initialized")


async def handle_sqs_event(
    event: dict,
    handler: EmailHandler,
) -> dict:
    """
    Handle SQS event
    """
    for record in event["Records"]:
        logger.info(f"Record: {record}")
        message_body = record["body"]
        if isinstance(message_body, bytes):
            message_body = message_body.decode("utf-8")
        event_data = SQSNotification(**json.loads(message_body))

        if event_data.data is not None:
            if event_data.email_event == EmailNotificationEvent.NEW_PARTICIPANT:
                # When new participant joins, send email to Care team
                name = "NotifyAdmins"
                logger.info(name)
                await handler.new_participant_joined_email(
                    participant_id=event_data.data["participant_id"]
                )
                return {}

            if event_data.email_event == EmailNotificationEvent.RESET_PASSWORD:
                # ResetPassword email
                logger.info(event_data.email_event.value)
                handler.send_reset_password_email(
                    email=event_data.data["email"],
                    reset_code=event_data.data["reset_code"],
                    user_id=event_data.data["sub"],
                    user_type=event_data.data["user_type"],
                )

                return {}

            if event_data.email_event == EmailNotificationEvent.WELCOME_PARTICIPANT:
                # Send confirm email to a signed-up user. Based on program.
                name = "program.EmailGreating"
                logger.info(name)
                await handler.send_welcome_email(
                    participant_id=event_data.data["participant_id"]
                )
                return {}

            if event_data.email_event == EmailNotificationEvent.DISENROLL_PARTICIPANT:
                # Send email to participant when they are disenrolled.
                name = "program.EmailDisenroll"
                logger.info(
                    f"{name} for participant: {event_data.data['participant_id']}"
                )
                await handler.send_disenrolled_email(
                    participant_id=event_data.data["participant_id"]
                )
                return {}

            if event_data.email_event == EmailNotificationEvent.CANCELLED_SESSION:
                # Send email to participant when a session has been cancelled.
                logger.info(
                    f"cancelling booking for participant: {event_data.data['email']}"
                )
                handler.send_cancelled_session_email(
                    email=event_data.data["email"],
                    first_name=event_data.data["first_name"],
                    class_name=event_data.data["class_name"],
                    class_date=event_data.data["class_date"],
                )
                return {}

            if event_data.email_event == EmailNotificationEvent.COHORT_ENDED:
                # Send email to participant when a cohort has ended.
                logger.info(
                    f"Sending email to participants in cohort: {event_data.data['cohort_id']}"
                )
                await handler.send_cohort_ended_email(
                    cohort_id=event_data.data["cohort_id"]
                )
                return {}

            if event_data.email_event == EmailNotificationEvent.TRANSTEK_TRACKING_INFO:
                logger.info(
                    f"Sending transtek tracking email to participant: {event_data.data['participant_id']}"
                )
                await handler.send_transtek_tracking_info_email(
                    participant_id=event_data.data["participant_id"],
                    tracking_number=event_data.data["tracking_number"],
                    tracking_url=event_data.data["tracking_url"],
                    carrier=event_data.data["carrier"],
                )
                return {}

            if (
                event_data.email_event
                == EmailNotificationEvent.CLASS_RECORDING_AVAILABLE
            ):
                logger.info(
                    f"Sending class recording available email for live session: {event_data.data['live_session_id']}"
                )
                await handler.send_class_recording_available_email(
                    live_session_id=event_data.data["live_session_id"]
                )
                return {}

            if event_data.email_event in [
                EmailNotificationEvent.CLASS_BOOKED,
                EmailNotificationEvent.CLASSES_UNLOCKED,
                EmailNotificationEvent.CANCELLED_SESSION_BY_USER,
            ]:
                await handler.send_participant_class_interaction_email(
                    participant_id=event_data.data["participant_id"],
                    live_session_id=event_data.data["live_session_id"],
                    event_type=event_data.email_event,
                )
                return {}

            logger.warning("Unsupported event type")
            return {"statusCode": 400, "body": json.dumps("Unsupported event source")}

    return {}


async def async_lambda_handler(event, context):
    """
    Async lambda handler
    """
    slc = SlackNotification(
        environment=settings.ENV,
        is_test=bool(settings.DEBUG),
        source=context.function_name,
    )
    rule_name = None

    await ensure_db_initialized()
    try:
        logger.info("Received event: " + json.dumps(event, indent=2))
        # Check if the event is from CloudWatch Events (Scheduled Event)
        handler = EmailHandler()
        logger.info(
            f"Connected to DB: host: {settings.POSTGRES_HOST}, DB: {settings.POSTGRES_DB}"
        )
        if "source" in event and event["source"] == "aws.events":
            logger.info(f"Scheduled Event received: {event}")
            resources = event.get("resources", [])
            rule_name = resources[0].split("/")[-1] if resources else None
            logger.info(f"Rule name: {rule_name}")
            if "Scales" in rule_name:
                slc.title = "Enrolled participants for last 24 hours"

                date_range_start = pendulum.now().subtract(days=1).to_date_string()
                date_range_end = pendulum.now().to_date_string()
                slc.details = f"Date range: {date_range_start} - {date_range_end}"

                slc.type = "OnboardedParticipants"
                slc.additional_info = "Download participants list by url."
                s3_path = await handler.send_new_participant_email()
                logger.info("File uploaded to S3 and sent by email")
                presigned_url = create_presigned_url_view(
                    bucket_name=settings.AWS_BUCKET_NAME,
                    object_name=s3_path,
                    expiration=604799,
                )
                slc.url = presigned_url
                logger.info("Link created")
                logger.info("Message published to SNS")
                return slc.model_dump()
            elif "NewModule" in rule_name:
                emails = await get_participants_with_cohort_starting_tomorrow()
                if emails:
                    emails = tuple(email["email"] for email in emails)
                    logger.info(
                        f"Found {len(emails)} participants with cohort starting tomorrow"
                    )
                    handler.send_cohort_starting_tomorrow_email(emails=emails)

                await handler.send_new_module_starting_email()
                logger.info("New module Email sent")
                return {}
            elif "CohortEndIn28Days" in rule_name:
                await handler.send_cohort_ending_in_28_days_email()
                logger.info("Cohort ending in 28 days Email sent")
                return {}
            elif "CohortEndTomorrowAdminReminder" in rule_name:
                response = await handler.send_cohorts_ending_tomorrow()
                if response:
                    logger.info("Admin cohort ending reminder Email sent")
                    return {}

                logger.info("No cohorts ending tomorrow")
                return {}
            elif "ClassIn24Hours" in rule_name:
                participants = await get_participants_with_class_in_24_hours()
                if not participants:
                    logger.info("No participants with class in 24 hours")
                    return {}

                for participant in participants:
                    handler.send_class_in_24_hours_reminder_email(
                        email=participant["email"],
                        first_name=participant["first_name"],
                        class_name=participant["class_name"],
                        class_date=participant["class_date"],
                    )

                logger.info("Class reminder Email sent")
                return {}

            else:
                raise ValueError(f"Unsupported rule name: {rule_name}")
        elif "Records" in event and event["Records"][0]["eventSource"] == "aws:sqs":
            result = await handle_sqs_event(event=event, handler=handler)
            return result
        else:
            return {"statusCode": 400, "body": json.dumps("Unsupported event source")}

    except Exception as e:
        slc.details = f"An error occurred: {str(e)}"
        slc.type = "Error"
        logger.exception(e)
        raise e

    finally:
        if rule_name and "Scales" in rule_name:
            publish_to_sns(
                sns_client=sns_client,
                sns_topic_arn=settings.SLACK_SNS_TOPIC_ARN,
                message=slc.model_dump_json(),
            )
        await close_db()
        global DB_INITIALIZED
        DB_INITIALIZED = False


def lambda_handler(event, context: LambdaContext):
    """
    Lambda handler
    """
    logger.info("Settings initiated")

    result = asyncio.run(async_lambda_handler(event, context))
    return result
